# Admin Implementation Guide

This guide provides comprehensive documentation for the admin functionality implementation in the Adori AI application.

## 🏗️ Architecture Overview

The admin system is built using Better Auth's admin plugin with the following key components:

### Core Technologies
- **Better Auth**: Authentication and admin plugin
- **React Query**: State management and caching
- **Shadcn UI**: User interface components
- **Next.js**: Server-side actions and API routes
- **Drizzle ORM**: Database operations

### Key Files Structure
```
src/
├── lib/
│   ├── auth.ts                    # Better Auth configuration
│   ├── auth-client.ts             # Client-side auth setup
│   ├── admin-utils.ts             # Client-side admin utilities
│   ├── admin-utils-server.ts      # Server-side admin utilities
│   └── toast.ts                   # Enhanced toast notifications
├── hooks/
│   └── use-admin.ts               # Admin-related React hooks
├── components/admin/
│   ├── impersonation-banner.tsx   # Impersonation status banner
│   ├── user-management.tsx        # User management interface
│   └── admin-menu.tsx             # Admin navigation menu
├── app/
│   ├── (dashboard)/admin/         # Admin pages
│   └── actions/admin.ts           # Server actions
└── config/
    └── nav-config.ts              # Navigation configuration
```

## 🔧 Configuration

### Better Auth Setup

The admin plugin is configured in `src/lib/auth.ts`:

```typescript
import { admin } from 'better-auth/plugins'

export const auth = betterAuth({
  plugins: [
    admin({
      defaultRole: 'user',
      adminRoles: ['admin', 'superadmin'],
      impersonationSessionDuration: 60 * 60, // 1 hour
      bannedUserMessage: 'Your account has been suspended.',
    }),
    // ... other plugins
  ],
})
```

### Database Schema

The admin plugin automatically adds these fields:

**User Table:**
- `role`: User role (default: 'user')
- `banned`: Ban status (default: false)
- `banReason`: Reason for ban (nullable)
- `banExpires`: Ban expiration date (nullable)

**Session Table:**
- `impersonatedBy`: ID of admin performing impersonation (nullable)

## 🛡️ Security Implementation

### Role-Based Access Control

Admin access is controlled through multiple layers:

1. **Database Level**: User role stored in database
2. **Server Level**: Server-side validation in actions
3. **Client Level**: UI restrictions and hooks
4. **Route Level**: Protected admin routes

### Permission Checking

```typescript
// Server-side check
const isAdmin = await isUserAdminServer(userId)

// Client-side check
const { isAdmin } = useIsAdmin()
```

## 👥 User Management Features

### Core Functionality

1. **User Listing**: Paginated list with search and filters
2. **Impersonation**: Admin can impersonate any user
3. **Ban/Unban**: Suspend user accounts with reasons
4. **Role Management**: Change user roles
5. **Session Management**: View and manage user sessions

### User Interface Components

#### Impersonation Banner
- Appears when admin is impersonating a user
- Shows admin ID and stop impersonation button
- Uses warning color scheme for visibility

#### User Management Table
- Displays user information with actions
- Search by name or email
- Role badges with color coding
- Action dropdown menus

#### Admin Menu
- Quick access to admin functions
- User search and impersonation
- Admin-only navigation items

## 🔄 State Management

### React Query Integration

All admin operations use React Query for:
- Caching user data
- Optimistic updates
- Error handling
- Loading states

### Custom Hooks

#### `useIsAdmin()`
Checks if current user has admin privileges.

#### `useImpersonation()`
Manages impersonation state and operations.

#### `useBanUser()`
Handles user ban/unban operations.

#### `useUserRole()`
Manages user role changes.

## 📡 API Integration

### Server Actions

Located in `src/app/actions/admin.ts`:

- `impersonateUserAction()`: Start impersonating a user
- `stopImpersonatingAction()`: Stop impersonation
- `banUserAction()`: Ban a user with reason
- `unbanUserAction()`: Remove user ban
- `setUserRoleAction()`: Change user role

### Better Auth API

Direct API calls for admin operations:
- `auth.api.impersonateUser()`
- `auth.api.banUser()`
- `auth.api.unbanUser()`
- `auth.api.setRole()`
- `auth.api.listUsers()`

## 🎨 User Experience

### Toast Notifications

Enhanced toast system with admin-specific helpers:

```typescript
// Success notifications
toast.adminSuccess('ban user', '<EMAIL>')

// Error notifications  
toast.adminError('impersonate user', error)

// Impersonation notifications
toast.impersonationStart('John Doe')
toast.impersonationStop()
```

### Loading States

All admin operations show appropriate loading states:
- Button spinners during actions
- Skeleton loaders for lists
- Progress indicators for long operations

### Error Handling

Comprehensive error handling with:
- User-friendly error messages
- Fallback UI states
- Retry mechanisms
- Detailed logging

## 🧪 Testing

### Automated Testing

Run the test suite:
```bash
bun scripts/test-admin-functionality.ts
```

Tests cover:
- Database schema validation
- Auth configuration
- Admin utilities
- API availability

### Manual Testing Checklist

1. **Admin Access**
   - [ ] Admin user can access admin routes
   - [ ] Regular user cannot access admin routes

2. **User Management**
   - [ ] List users with pagination
   - [ ] Search users by name/email
   - [ ] View user details and status

3. **Impersonation**
   - [ ] Start impersonation
   - [ ] Banner appears with correct info
   - [ ] Stop impersonation works
   - [ ] Session switches correctly

4. **User Actions**
   - [ ] Ban user with reason
   - [ ] Unban user
   - [ ] Change user role
   - [ ] All actions show feedback

## 🚀 Deployment Considerations

### Environment Variables

Ensure these are set in production:
```env
BETTER_AUTH_SECRET=your-secret-key
DATABASE_URL=your-database-url
NEXT_PUBLIC_APP_URL=your-app-url
```

### Security Checklist

- [ ] Admin routes protected in production
- [ ] Sensitive operations logged
- [ ] Rate limiting configured
- [ ] HTTPS enforced
- [ ] Session security configured

### Performance Optimization

- [ ] Database indexes on user queries
- [ ] Pagination for large user lists
- [ ] Efficient cache strategies
- [ ] Optimized bundle size

## 🐛 Troubleshooting

### Common Issues

**Admin functions not working:**
- Check user role in database
- Verify Better Auth configuration
- Check browser console for errors

**Impersonation not working:**
- Verify session management
- Check impersonation duration setting
- Ensure proper cookie configuration

**UI not updating:**
- Check React Query cache invalidation
- Verify state management hooks
- Look for JavaScript errors

### Debug Commands

```bash
# Check database schema
bun scripts/check-db-schema.ts

# Test auth configuration
bun scripts/test-auth-config.ts

# Validate admin setup
bun scripts/test-admin-functionality.ts
```

## 📚 Additional Resources

- [Better Auth Documentation](https://better-auth.com)
- [React Query Documentation](https://tanstack.com/query)
- [Shadcn UI Components](https://ui.shadcn.com)
- [Next.js Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions)

---

**Last Updated:** 2025-01-21  
**Version:** 1.0.0  
**Author:** Adori AI Development Team
