# Admin Functionality Quick Reference

## 🚀 Quick Start

### Check if user is admin
```typescript
// Client-side
const { isAdmin } = useIsAdmin()

// Server-side
const isAdmin = await isUserAdminServer(userId)
```

### Impersonation
```typescript
// Start impersonation
const impersonate = useImpersonateUser()
impersonate.mutate(userId)

// Check impersonation status
const { isImpersonating, impersonatingAdminId } = useImpersonation()

// Stop impersonation
const { stopImpersonating } = useImpersonation()
stopImpersonating()
```

### User Management
```typescript
// Ban user
const { banUser } = useBanUser()
banUser({ userId, banReason: 'Spam', banExpiresIn: 86400 })

// Unban user
const { unbanUser } = useBanUser()
unbanUser(userId)

// Change role
const setRole = useUserRole()
setRole.mutate({ userId, role: 'admin' })
```

## 🎯 Key Components

### Impersonation Banner
```typescript
import { ImpersonationBanner } from '@/components/admin/impersonation-banner'

// Add to layout
<ImpersonationBanner />
```

### User Management Interface
```typescript
import { UserManagement } from '@/components/admin/user-management'

// Admin page
<UserManagement />
```

### Admin Menu
```typescript
import { AdminMenu } from '@/components/admin/admin-menu'

// Navigation
<AdminMenu />
```

## 🔧 Server Actions

```typescript
import {
  impersonateUserAction,
  stopImpersonatingAction,
  banUserAction,
  unbanUserAction,
  setUserRoleAction
} from '@/app/actions/admin'

// Use in server components or forms
const result = await impersonateUserAction(userId)
```

## 🎨 Toast Notifications

```typescript
import { toast } from '@/lib/toast'

// Admin-specific toasts
toast.adminSuccess('ban user', '<EMAIL>')
toast.adminError('impersonate user', error)
toast.impersonationStart('John Doe')
toast.impersonationStop()
```

## 🛡️ Route Protection

```typescript
// Protect admin routes
import { redirect } from 'next/navigation'
import { auth } from '@/lib/auth'
import { isUserAdminServer } from '@/lib/admin-utils-server'

export default async function AdminPage() {
  const session = await auth.api.getSession()
  if (!session?.user) redirect('/login')
  
  const isAdmin = await isUserAdminServer(session.user.id)
  if (!isAdmin) redirect('/')
  
  return <AdminContent />
}
```

## 📊 Database Queries

```typescript
import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import { eq } from 'drizzle-orm'

// Get user with admin fields
const user = await db.select().from(schema.user)
  .where(eq(schema.user.id, userId))
  .limit(1)

// Check if user is banned
const isBanned = user[0]?.banned || false

// Get impersonation sessions
const sessions = await db.select().from(schema.session)
  .where(eq(schema.session.impersonatedBy, adminId))
```

## 🔍 Debugging

### Check Admin Status
```typescript
// In browser console
console.log('Admin status:', await fetch('/api/admin/status').then(r => r.json()))
```

### Validate Configuration
```bash
# Run tests
bun scripts/test-admin-functionality.ts

# Check database
bun scripts/check-db-schema.ts
```

### Common Fixes
```typescript
// Clear admin cache
import { clearSessionCache } from '@/lib/admin-utils'
clearSessionCache()

// Refresh queries
queryClient.invalidateQueries({ queryKey: ['admin-users'] })
queryClient.invalidateQueries({ queryKey: ['admin-status'] })
```

## 🎛️ Configuration

### Better Auth Admin Plugin
```typescript
admin({
  defaultRole: 'user',
  adminRoles: ['admin', 'superadmin'],
  impersonationSessionDuration: 60 * 60, // 1 hour
  bannedUserMessage: 'Account suspended.',
})
```

### Navigation Config
```typescript
// Add admin items to nav-config.ts
{
  title: 'Admin',
  url: '/admin',
  icon: Shield,
  adminOnly: true,
  items: [
    { title: 'Users', url: '/admin/users', icon: Users },
    { title: 'Settings', url: '/admin/settings', icon: Settings },
  ]
}
```

## 🚨 Error Handling

### Common Error Patterns
```typescript
try {
  const result = await adminAction()
  if (!result.success) {
    toast.adminError('operation', result.error)
    return
  }
  toast.adminSuccess('operation')
} catch (error) {
  console.error('Admin operation failed:', error)
  toast.adminError('operation', error)
}
```

### Error Boundaries
```typescript
// Wrap admin components
<ErrorBoundary fallback={<AdminErrorFallback />}>
  <AdminComponent />
</ErrorBoundary>
```

## 📱 Responsive Design

### Mobile Considerations
```typescript
// Use responsive classes
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Admin cards */}
</div>

// Mobile-friendly dialogs
<Dialog>
  <DialogContent className="sm:max-w-md">
    {/* Content */}
  </DialogContent>
</Dialog>
```

## 🔐 Security Best Practices

### Input Validation
```typescript
// Validate admin actions
if (!userId || typeof userId !== 'string') {
  throw new Error('Invalid user ID')
}

// Sanitize user input
const sanitizedReason = banReason?.trim().slice(0, 500)
```

### Rate Limiting
```typescript
// Add rate limiting to admin actions
import { rateLimit } from '@/lib/rate-limit'

export async function adminAction() {
  await rateLimit('admin-action', { max: 10, window: '1m' })
  // ... action logic
}
```

## 📈 Performance Tips

### Optimize Queries
```typescript
// Use pagination
const users = await db.select().from(schema.user)
  .limit(pageSize)
  .offset(page * pageSize)

// Index frequently queried fields
// Add indexes for: email, role, banned, createdAt
```

### Cache Strategies
```typescript
// Cache admin data
const { data: users } = useQuery({
  queryKey: ['admin-users', page, search],
  queryFn: () => fetchUsers(page, search),
  staleTime: 5 * 60 * 1000, // 5 minutes
})
```

---

**Need help?** Check the full [Admin Implementation Guide](./admin-implementation-guide.md)
