#!/usr/bin/env bun

/**
 * Admin Functionality Test Script
 * 
 * This script tests the admin functionality to ensure everything is working correctly.
 * Run with: bun scripts/test-admin-functionality.ts
 */

import { auth } from '../src/lib/auth'
import { isUserAdminServer } from '../src/lib/admin-utils-server'
import { db } from '../src/lib/db'
import { schema } from '../src/db/schema'
import { eq } from 'drizzle-orm'

interface TestResult {
  name: string
  passed: boolean
  error?: string
  details?: any
}

class AdminFunctionalityTester {
  private results: TestResult[] = []

  private addResult(name: string, passed: boolean, error?: string, details?: any) {
    this.results.push({ name, passed, error, details })
    const status = passed ? '✅ PASS' : '❌ FAIL'
    console.log(`${status}: ${name}`)
    if (error) {
      console.log(`   Error: ${error}`)
    }
    if (details) {
      console.log(`   Details:`, details)
    }
  }

  async testDatabaseSchema() {
    try {
      // Test if admin fields exist in user table
      const testUser = await db.select().from(schema.user).limit(1)
      
      if (testUser.length > 0) {
        const user = testUser[0] as any
        const hasRoleField = 'role' in user
        const hasBannedField = 'banned' in user
        const hasBanReasonField = 'banReason' in user
        const hasBanExpiresField = 'banExpires' in user
        
        this.addResult(
          'Database Schema - User table has admin fields',
          hasRoleField && hasBannedField && hasBanReasonField && hasBanExpiresField,
          hasRoleField && hasBannedField && hasBanReasonField && hasBanExpiresField 
            ? undefined 
            : 'Missing admin fields in user table',
          {
            role: hasRoleField,
            banned: hasBannedField,
            banReason: hasBanReasonField,
            banExpires: hasBanExpiresField
          }
        )
      } else {
        this.addResult('Database Schema - User table has admin fields', false, 'No users found in database')
      }

      // Test if session table has impersonation field
      const testSession = await db.select().from(schema.session).limit(1)
      if (testSession.length > 0) {
        const session = testSession[0] as any
        const hasImpersonatedByField = 'impersonatedBy' in session
        
        this.addResult(
          'Database Schema - Session table has impersonation field',
          hasImpersonatedByField,
          hasImpersonatedByField ? undefined : 'Missing impersonatedBy field in session table'
        )
      } else {
        this.addResult('Database Schema - Session table has impersonation field', false, 'No sessions found in database')
      }
    } catch (error) {
      this.addResult('Database Schema Test', false, `Database error: ${error}`)
    }
  }

  async testAuthConfiguration() {
    try {
      // Test if auth context is available
      const ctx = await auth.$context
      this.addResult('Auth Configuration - Context available', !!ctx)

      // Test if admin plugin is configured
      const hasAdminPlugin = ctx.options?.plugins?.some((plugin: any) => plugin.id === 'admin')
      this.addResult('Auth Configuration - Admin plugin configured', !!hasAdminPlugin)

      // Test if organization plugin is configured
      const hasOrgPlugin = ctx.options?.plugins?.some((plugin: any) => plugin.id === 'organization')
      this.addResult('Auth Configuration - Organization plugin configured', !!hasOrgPlugin)

    } catch (error) {
      this.addResult('Auth Configuration Test', false, `Auth configuration error: ${error}`)
    }
  }

  async testAdminUtilities() {
    try {
      // Create a test admin user
      const testAdminId = 'test-admin-' + Date.now()
      await db.insert(schema.user).values({
        id: testAdminId,
        email: '<EMAIL>',
        name: 'Test Admin',
        role: 'admin',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      // Test admin check
      const isAdmin = await isUserAdminServer(testAdminId)
      this.addResult('Admin Utilities - Admin role detection', isAdmin)

      // Create a test regular user
      const testUserId = 'test-user-' + Date.now()
      await db.insert(schema.user).values({
        id: testUserId,
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      // Test non-admin check
      const isNotAdmin = await isUserAdminServer(testUserId)
      this.addResult('Admin Utilities - Non-admin role detection', !isNotAdmin)

      // Clean up test users
      await db.delete(schema.user).where(eq(schema.user.id, testAdminId))
      await db.delete(schema.user).where(eq(schema.user.id, testUserId))

    } catch (error) {
      this.addResult('Admin Utilities Test', false, `Admin utilities error: ${error}`)
    }
  }

  async testBetterAuthAPI() {
    try {
      // Test if Better Auth admin API endpoints are available
      const apiMethods = [
        'impersonateUser',
        'stopImpersonating',
        'banUser',
        'unbanUser',
        'setRole',
        'listUsers'
      ]

      for (const method of apiMethods) {
        const hasMethod = typeof (auth.api as any)[method] === 'function'
        this.addResult(`Better Auth API - ${method} method available`, hasMethod)
      }

    } catch (error) {
      this.addResult('Better Auth API Test', false, `API test error: ${error}`)
    }
  }

  async runAllTests() {
    console.log('🧪 Starting Admin Functionality Tests...\n')

    await this.testDatabaseSchema()
    await this.testAuthConfiguration()
    await this.testAdminUtilities()
    await this.testBetterAuthAPI()

    // Summary
    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length
    const failed = total - passed

    console.log('\n📊 Test Summary:')
    console.log(`   Total Tests: ${total}`)
    console.log(`   Passed: ${passed}`)
    console.log(`   Failed: ${failed}`)
    console.log(`   Success Rate: ${((passed / total) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ Failed Tests:')
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.name}: ${r.error || 'Unknown error'}`))
    }

    return failed === 0
  }
}

// Run the tests
async function main() {
  const tester = new AdminFunctionalityTester()
  const success = await tester.runAllTests()
  
  if (success) {
    console.log('\n🎉 All admin functionality tests passed!')
    process.exit(0)
  } else {
    console.log('\n💥 Some tests failed. Please check the issues above.')
    process.exit(1)
  }
}

// Handle CLI execution
if (import.meta.main) {
  main().catch(error => {
    console.error('Test execution failed:', error)
    process.exit(1)
  })
}
