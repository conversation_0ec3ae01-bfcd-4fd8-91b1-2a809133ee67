'use client'

import React, { useRef, useEffect } from 'react'
import { MainVideoArea } from './main-video-area'
import { RemotionPreviewControls } from './remotion-preview-controls'
import { PlayerRef } from '@remotion/player'
import { useVideoStore } from '@/store/video-store'

interface VideoPlayerContainerProps {
  isFullscreen: boolean
  onFullscreenToggle: () => void
}

export function VideoPlayerContainer({
  isFullscreen,
  onFullscreenToggle,
}: VideoPlayerContainerProps) {
  const playerRef = useRef<PlayerRef>(null)
  const { setPlayerRef } = useVideoStore()

  // Set the player ref in the store when it changes
  useEffect(() => {
    setPlayerRef(playerRef)
    return () => setPlayerRef(null)
  }, [setPlayerRef])

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      {/* Video area takes available space but leaves room for controls */}
      <div className='flex-1 min-h-0 overflow-hidden'>
        <MainVideoArea playerRef={playerRef} isFullscreen={isFullscreen} />
      </div>

      {/* Controls always visible at bottom - especially important in fullscreen */}
      <div className='flex-shrink-0'>
        <RemotionPreviewControls
          fullscreenToggle={onFullscreenToggle}
          playerRef={playerRef}
        />
      </div>
    </div>
  )
}
