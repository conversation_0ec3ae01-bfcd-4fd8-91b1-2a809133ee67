'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Loader2,
  Undo2,
  Redo2,
  History,
  Pencil,
  Cloud,
  CloudOff,
  Folder,
  Download,
} from 'lucide-react'
import { useVideoStore } from '@/store/video-store'
import { useCaptionStylesStore } from '@/store'
import { useState, useRef, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { UserMenu } from '@/components/user-menu'
import { useTheme } from 'next-themes'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { toast } from '@/lib/toast'

interface VideoEditorHeaderProps {
  onShowRecents?: () => void
  onShowExport?: () => void
  syncStatus?: {
    isSaving: boolean
    lastSaved: Date | null
    hasUnsavedChanges: boolean
    forceSave: () => Promise<void>
  }
}

export function VideoEditorHeader({
  onShowRecents,
  onShowExport,
  syncStatus,
}: VideoEditorHeaderProps) {
  const {
    scenes,
    subtitlePosition,
    orientation,
    isLoading,
    setLoading,
    undo,
    redo,
    canUndo,
    canRedo,
    selectedMusic,
    musicVolume,
    musicEnabled,
    project,
    updateProjectName,
  } = useVideoStore()

  const { getSelectedStyle } = useCaptionStylesStore()

  const [renderProgress, setRenderProgress] = useState<string>('')
  const [isEditingName, setIsEditingName] = useState(false)
  const [editedName, setEditedName] = useState(project?.projectName || '')
  const inputRef = useRef<HTMLInputElement>(null)
  const editAreaRef = useRef<HTMLDivElement>(null)

  // Handle export click - always show modal, gating happens inside modal
  const handleExportClick = () => {
    onShowExport?.()
  }

  // Focus input when editing starts
  useEffect(() => {
    if (isEditingName && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isEditingName])

  // Handle save on blur or enter
  const handleSaveName = useCallback(() => {
    if (project && editedName.trim() && editedName !== project.projectName) {
      updateProjectName(editedName.trim())
    }
    setIsEditingName(false)
  }, [project, editedName, updateProjectName])

  // Click outside handler for editing area
  useEffect(() => {
    if (!isEditingName) return
    function handleClickOutside(event: MouseEvent) {
      if (
        editAreaRef.current &&
        !editAreaRef.current.contains(event.target as Node)
      ) {
        handleSaveName()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isEditingName, editedName, project, handleSaveName])

  // Filter valid scenes for preview
  const validScenes = scenes.filter(
    scene => typeof scene.duration === 'number' && scene.duration > 0
  )

  // Render functionality from video-studio
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleRender = async () => {
    if (validScenes.length === 0) {
      toast.warning('Please add scenes with voiceover before rendering')
      return
    }

    setLoading(true)
    const renderToastId = toast.renderProgress('Preparing video...')

    try {
      // Get the most current subtitle position directly from localStorage
      let currentPosition = { ...subtitlePosition }
      try {
        const savedPosition = localStorage.getItem('subtitlePosition')
        if (savedPosition) {
          currentPosition = JSON.parse(savedPosition)
          console.log(
            '🎯 Using most current position for rendering:',
            currentPosition
          )
        }
      } catch (e) {
        console.error('Error getting current subtitle position', e)
      }

      // Calculate composition dimensions based on current orientation
      let compositionWidth: number
      let compositionHeight: number

      switch (orientation) {
        case 'landscape':
          compositionWidth = 1920
          compositionHeight = 1080
          break
        case 'square':
          compositionWidth = 1080
          compositionHeight = 1080
          break
        case 'portrait':
          compositionWidth = 1080
          compositionHeight = 1920
          break
        default:
          compositionWidth = 1920
          compositionHeight = 1080
      }

      toast.renderProgress('Rendering video...', renderToastId)
      console.log(
        'Rendering with orientation:',
        orientation,
        'dimensions:',
        compositionWidth,
        'x',
        compositionHeight
      )

      const res = await fetch('/api/remotion-render', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          scenes: validScenes,
          subtitlePosition: currentPosition,
          orientation: orientation,
          compositionWidth: compositionWidth,
          compositionHeight: compositionHeight,
          selectedMusic: selectedMusic,
          musicVolume: musicVolume,
          musicEnabled: musicEnabled,
          captionStyle: getSelectedStyle(), // Add caption style to render data
          speech: project?.speech, // Include speech object for audio/podcast workflows
        }),
      })

      if (!res.ok) {
        const errorText = await res.text()
        throw new Error(`Failed to render video: ${errorText}`)
      }

      toast.renderProgress('Downloading...', renderToastId)
      const blob = await res.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `adori-video-${orientation}-${Date.now()}.mp4`
      document.body.appendChild(a)
      a.click()
      a.remove()
      window.URL.revokeObjectURL(url)

      toast.dismiss(renderToastId)
      toast.success('Video rendered and downloaded successfully!')
    } catch (err) {
      console.error('Render error:', err)
      toast.dismiss(renderToastId)
      toast.apiError(err, 'Failed to render video')
      setRenderProgress('')
    } finally {
      setLoading(false)
    }
  }

  const { theme } = useTheme()
  const logoSrc =
    theme === 'dark' ? '/adori-logo-dark.svg' : '/adori-logo-light.svg'

  const router = useRouter()

  // Helper function to get sync icon and color
  const getSyncIconAndColor = () => {
    if (!syncStatus) return { icon: Cloud, color: 'text-muted-foreground' }

    if (syncStatus.isSaving) {
      return { icon: Loader2, color: 'text-blue-600' }
    }

    if (syncStatus.hasUnsavedChanges) {
      return { icon: CloudOff, color: 'text-amber-600' }
    }

    return { icon: Cloud, color: 'text-green-600' }
  }

  const { icon: SyncIcon, color: syncColor } = getSyncIconAndColor()

  return (
    <header className='sticky top-0 z-30 h-14 md:h-16 border-b flex items-center justify-between px-2 md:px-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
      {/* Left Section */}
      <div className='flex items-center gap-2 md:gap-4'>
        {/* <SidebarTrigger /> */}
        <div className='flex items-center gap-2'>
          <div className='w-7 h-7 md:w-8 md:h-8 rounded-lg flex items-center justify-center'>
            <span className='text-primary-foreground font-bold text-xs md:text-sm'>
              <Image
                src={logoSrc}
                alt='Adori AI Logo'
                width={32}
                height={32}
                onClick={() => {
                  if (syncStatus?.hasUnsavedChanges && !syncStatus?.isSaving) {
                    const shouldLeave = window.confirm(
                      'You have unsaved changes in your video project. Are you sure you want to leave without saving?'
                    )
                    if (shouldLeave) {
                      router.push('/')
                    }
                  } else {
                    router.push('/')
                  }
                }}
                className='cursor-pointer'
              />
            </span>
          </div>
          <div>
            <h1 className='font-semibold text-base md:text-lg'>Video Editor</h1>
            <div className='flex items-center gap-1 md:gap-2' ref={editAreaRef}>
              <Folder className='h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0' />
              {isEditingName ? (
                <div className='flex items-center gap-1'>
                  <input
                    ref={inputRef}
                    className='text-xs md:text-sm font-medium truncate max-w-[80px] sm:max-w-none bg-transparent border-b border-primary outline-none px-1'
                    value={editedName}
                    maxLength={40}
                    onChange={e => setEditedName(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        handleSaveName()
                      }
                      if (e.key === 'Escape') setIsEditingName(false)
                    }}
                  />
                  <Button
                    variant='ghost'
                    size='icon'
                    className='h-5 w-5 p-0 hover:bg-transparent'
                    tabIndex={-1}
                    aria-label='Save'
                    onMouseDown={e => e.preventDefault()} // Prevent input blur
                    onClick={handleSaveName}
                  >
                    <Pencil className='h-3 w-3 text-muted-foreground' />
                  </Button>
                </div>
              ) : (
                <div className='flex items-center gap-1'>
                  <span
                    className='text-xs md:text-sm font-medium truncate max-w-[80px] sm:max-w-none cursor-pointer'
                    title={project?.projectName || 'Untitled Project'}
                  >
                    {project?.projectName || 'Untitled Project'}
                  </span>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='h-5 w-5 p-0 hover:bg-transparent'
                    onClick={() => {
                      setEditedName(project?.projectName || '')
                      setIsEditingName(true)
                    }}
                    tabIndex={-1}
                    aria-label='Edit'
                  >
                    <Pencil className='h-3 w-3 text-muted-foreground' />
                  </Button>
                </div>
              )}
              {/* Render Progress in header */}
              {renderProgress && (
                <span className='text-[10px] md:text-xs text-muted-foreground ml-1 md:ml-2'>
                  • {renderProgress}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Save Status - Prominent Position */}
        {syncStatus && (
          <div className='flex items-center gap-2 border-l pl-2 ml-2'>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant='ghost'
                        size='icon'
                        className={cn(
                          'h-8 w-8 transition-colors',
                          syncStatus.hasUnsavedChanges || syncStatus.isSaving
                            ? 'hover:bg-muted'
                            : 'hover:bg-green-50 dark:hover:bg-green-950'
                        )}
                        onClick={
                          syncStatus.hasUnsavedChanges
                            ? syncStatus.forceSave
                            : undefined
                        }
                      >
                        <SyncIcon
                          className={cn(
                            'h-4 w-4',
                            syncColor,
                            syncStatus.isSaving && 'animate-spin'
                          )}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className='w-80' align='start'>
                      <div className='space-y-3'>
                        <div className='flex items-center justify-between'>
                          <span className='font-medium'>
                            Project Sync Status
                          </span>
                          <Badge
                            variant={
                              syncStatus.isSaving
                                ? 'default'
                                : syncStatus.hasUnsavedChanges
                                  ? 'secondary'
                                  : 'outline'
                            }
                            className={cn(
                              syncStatus.isSaving &&
                                'bg-blue-100 text-blue-800 dark:bg-blue-950 dark:text-blue-200',
                              syncStatus.hasUnsavedChanges &&
                                !syncStatus.isSaving &&
                                'bg-amber-100 text-amber-800 dark:bg-amber-950 dark:text-amber-200',
                              !syncStatus.hasUnsavedChanges &&
                                !syncStatus.isSaving &&
                                'bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-200'
                            )}
                          >
                            {syncStatus.isSaving
                              ? 'Syncing'
                              : syncStatus.hasUnsavedChanges
                                ? 'Unsaved'
                                : 'Synced'}
                          </Badge>
                        </div>

                        {syncStatus.lastSaved && (
                          <div className='space-y-1'>
                            <div className='text-sm text-muted-foreground'>
                              Last saved:{' '}
                              {syncStatus.lastSaved.toLocaleString()}
                            </div>
                            <div className='text-xs text-muted-foreground'>
                              {new Date().getTime() -
                                syncStatus.lastSaved.getTime() <
                              60000
                                ? 'Just now'
                                : `${Math.floor((new Date().getTime() - syncStatus.lastSaved.getTime()) / 60000)} minutes ago`}
                            </div>
                          </div>
                        )}

                        <div className='text-sm text-muted-foreground'>
                          {syncStatus.isSaving
                            ? 'Your changes are being saved to the cloud...'
                            : syncStatus.hasUnsavedChanges
                              ? 'You have unsaved changes that will be automatically saved.'
                              : 'All changes are saved and synced.'}
                        </div>

                        {syncStatus.hasUnsavedChanges && (
                          <Button
                            onClick={syncStatus.forceSave}
                            disabled={syncStatus.isSaving}
                            size='sm'
                            className='w-full'
                          >
                            {syncStatus.isSaving ? (
                              <>
                                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Cloud className='mr-2 h-4 w-4' />
                                Save Now
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {syncStatus.isSaving
                      ? 'Saving changes...'
                      : syncStatus.hasUnsavedChanges
                        ? 'Click to save changes'
                        : 'All changes saved'}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Save status text for larger screens */}
            <div className='hidden md:flex flex-col'>
              <span className={cn('text-xs font-medium', syncColor)}>
                {syncStatus.isSaving
                  ? 'Saving...'
                  : syncStatus.hasUnsavedChanges
                    ? 'Unsaved changes'
                    : 'All saved'}
              </span>
              {syncStatus.lastSaved && !syncStatus.isSaving && (
                <span className='text-[10px] text-muted-foreground'>
                  {new Date().getTime() - syncStatus.lastSaved.getTime() < 60000
                    ? 'Just now'
                    : `${Math.floor((new Date().getTime() - syncStatus.lastSaved.getTime()) / 60000)}m ago`}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Undo/Redo Buttons */}
        <div className='hidden sm:flex items-center gap-1 border-l pl-2 ml-2'>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-7 w-7'
                  onClick={undo}
                  disabled={!canUndo()}
                >
                  <Undo2 className='h-3.5 w-3.5' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Undo (Ctrl+Z)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-7 w-7'
                  onClick={redo}
                  disabled={!canRedo()}
                >
                  <Redo2 className='h-3.5 w-3.5' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Redo (Ctrl+Y)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Right Section */}
      <div className='flex items-center gap-1 md:gap-3'>
        {/* Mobile Undo/Redo Buttons */}
        <div className='flex sm:hidden items-center mr-1'>
          <Button
            variant='ghost'
            size='icon'
            className='h-7 w-7'
            onClick={undo}
            disabled={!canUndo()}
          >
            <Undo2 className='h-3.5 w-3.5' />
          </Button>
          <Button
            variant='ghost'
            size='icon'
            className='h-7 w-7'
            onClick={redo}
            disabled={!canRedo()}
          >
            <Redo2 className='h-3.5 w-3.5' />
          </Button>
        </div>

        {/* Desktop Buttons */}
        <div className='hidden sm:flex items-center gap-1 md:gap-3'>
          <Button
            variant='outline'
            size='icon'
            className='h-8 w-8 sm:h-8 sm:w-auto sm:px-3'
            onClick={onShowRecents}
          >
            <History className='h-3 w-3 md:h-4 md:w-4 sm:mr-2' />
            <span className='hidden sm:inline'>Recent exports</span>
          </Button>
          <Button
            onClick={handleExportClick}
            disabled={isLoading || validScenes.length === 0}
            size='icon'
            className='h-8 w-8 sm:h-8 sm:w-auto sm:px-3 bg-primary text-primary-foreground hover:bg-primary/90'
          >
            {isLoading ? (
              <>
                <Loader2 className='h-3 w-3 md:h-4 md:w-4 sm:mr-2 animate-spin' />
                <span className='hidden sm:inline'>Exporting...</span>
              </>
            ) : (
              <>
                <Download className='h-3 w-3 md:h-4 md:w-4 sm:mr-2' />
                <span className='hidden sm:inline'>Export video</span>
              </>
            )}
          </Button>
        </div>

        {/* Mobile Icon Buttons */}
        <div className='flex sm:hidden items-center gap-1'>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-8 w-8'
                  onClick={onShowRecents}
                  aria-label='Recent exports'
                >
                  <History className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Recent exports</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='h-8 w-8'
                  onClick={handleExportClick}
                  disabled={isLoading || validScenes.length === 0}
                  aria-label={isLoading ? 'Rendering video...' : 'Export video'}
                >
                  {isLoading ? (
                    <Loader2 className='h-4 w-4 animate-spin' />
                  ) : (
                    <Download className='h-4 w-4' />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isLoading ? 'Exporting...' : 'Export video'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className='ml-1'>
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
