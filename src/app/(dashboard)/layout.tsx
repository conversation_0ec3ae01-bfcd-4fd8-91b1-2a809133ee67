import {
  <PERSON><PERSON><PERSON><PERSON>er,
  DashboardPrefetch<PERSON>rovider,
  DashboardSidebarWrapper,
} from './_components'
import { ImpersonationBanner } from '@/components/admin/impersonation-banner'

// Page configuration with titles and icon names (strings instead of components)
const PAGE_CONFIG: Record<string, { title: string; icon: string }> = {
  '/': { title: 'Create Video', icon: 'Video' },
  '/create-video': { title: 'Create Video', icon: 'Video' },
  '/create-video/idea': { title: 'Idea to Video', icon: 'Lightbulb' },
  '/create-video/blog': { title: 'Blog to Video', icon: 'Link' },
  '/create-video/text': { title: 'Text to Video', icon: 'FileText' },
  '/create-video/pdf': { title: 'PDF to Video', icon: 'FileUp' },
  '/create-video/audio': { title: 'Audio to Video', icon: 'AudioLines' },
  '/create-video/podcast': { title: 'Podcast to Video', icon: 'Radio' },
  '/my-videos': { title: 'My Videos', icon: 'FileVideo' },
  '/projects': { title: 'Projects', icon: 'FolderOpen' },
  '/billing': { title: 'Billing', icon: 'CreditCard' },
  '/settings': { title: 'Settings', icon: 'Settings' },
  '/admin': { title: 'Admin Dashboard', icon: 'Shield' },
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Define page configuration (now in server component)
  const defaultConfig = {
    title: 'Dashboard',
    icon: 'Video',
  }

  return (
    <DashboardPrefetchProvider>
      <DashboardSidebarWrapper>
        <DashboardHeader
          pageConfig={PAGE_CONFIG}
          defaultConfig={defaultConfig}
        />
        <div className='flex-1 overflow-auto bg-background'>
          <ImpersonationBanner />
          <main className='p-4 md:p-6 lg:p-8'>{children}</main>
        </div>
      </DashboardSidebarWrapper>
    </DashboardPrefetchProvider>
  )
}
