'use client'

import { useIsAdmin } from '@/hooks/use-admin'
import { UserManagement } from '@/components/admin/user-management'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Shield, Users, Settings, Activity } from 'lucide-react'
import { Loader2 } from 'lucide-react'

export default function AdminPage() {
  const { isAdmin, isLoading } = useIsAdmin()

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>Checking permissions...</span>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Card className='w-full max-w-md'>
          <CardHeader className='text-center'>
            <Shield className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to access the admin panel.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className='container mx-auto py-8 space-y-8'>
      <div>
        <h1 className='text-3xl font-bold tracking-tight'>Admin Dashboard</h1>
        <p className='text-muted-foreground'>
          Manage users, permissions, and system settings.
        </p>
      </div>

      <Tabs defaultValue='users' className='space-y-6'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='users' className='flex items-center gap-2'>
            <Users className='h-4 w-4' />
            Users
          </TabsTrigger>
          <TabsTrigger value='activity' className='flex items-center gap-2'>
            <Activity className='h-4 w-4' />
            Activity
          </TabsTrigger>
          <TabsTrigger value='settings' className='flex items-center gap-2'>
            <Settings className='h-4 w-4' />
            Settings
          </TabsTrigger>
          <TabsTrigger value='permissions' className='flex items-center gap-2'>
            <Shield className='h-4 w-4' />
            Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value='users' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                View and manage all users in the system. You can impersonate
                users, change roles, and ban/unban accounts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='activity' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>System Activity</CardTitle>
              <CardDescription>
                Monitor system activity and user actions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8 text-muted-foreground'>
                Activity monitoring coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='settings' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>
                Configure system-wide settings and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8 text-muted-foreground'>
                System settings coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='permissions' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Permission Management</CardTitle>
              <CardDescription>
                Manage roles and permissions for different user types.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8 text-muted-foreground'>
                Permission management coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
