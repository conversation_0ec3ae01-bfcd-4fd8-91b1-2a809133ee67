'use client'

import { useIsAdmin } from '@/hooks/use-admin'
import { UserManagement } from '@/components/admin/user-management'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Shield, Users, Settings, Activity, AlertCircle } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function AdminPage() {
  const { isAdmin, isLoading } = useIsAdmin()

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>Checking permissions...</span>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Card className='w-full max-w-md'>
          <CardHeader className='text-center'>
            <Shield className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to access the admin panel.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className='container mx-auto py-6 space-y-6'>
      {/* Header Section */}

      <Tabs defaultValue='users' className='space-y-6'>
        {/* TODO: for future updates on dashboard */}
        {/* <TabsList className='grid w-full grid-cols-4 h-11'>
          <TabsTrigger
            value='users'
            className='flex items-center gap-2 text-sm'
          >
            <Users className='h-4 w-4' />
            Users
          </TabsTrigger>
          <TabsTrigger
            value='activity'
            className='flex items-center gap-2 text-sm'
          >
            <Activity className='h-4 w-4' />
            Activity
          </TabsTrigger>
          <TabsTrigger
            value='settings'
            className='flex items-center gap-2 text-sm'
          >
            <Settings className='h-4 w-4' />
            Settings
          </TabsTrigger>
          <TabsTrigger
            value='permissions'
            className='flex items-center gap-2 text-sm'
          >
            <Shield className='h-4 w-4' />
            Permissions
          </TabsTrigger>
        </TabsList> */}

        <TabsContent value='users' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                View and manage all users in the system. You can impersonate
                users, change roles, and ban/unban accounts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='activity' className='space-y-6'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Activity className='h-5 w-5' />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Latest user actions and system events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    Activity monitoring is not yet implemented. This feature
                    will track user logins, admin actions, and system events.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Activity Statistics</CardTitle>
                <CardDescription>
                  System usage metrics and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between items-center p-3 bg-muted/50 rounded-lg'>
                    <span className='text-sm font-medium'>
                      Daily Active Users
                    </span>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 bg-muted/50 rounded-lg'>
                    <span className='text-sm font-medium'>Admin Actions</span>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 bg-muted/50 rounded-lg'>
                    <span className='text-sm font-medium'>System Errors</span>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='settings' className='space-y-6'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Settings className='h-5 w-5' />
                  General Settings
                </CardTitle>
                <CardDescription>
                  Basic system configuration options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between items-center p-3 border rounded-lg'>
                    <div>
                      <p className='text-sm font-medium'>User Registration</p>
                      <p className='text-xs text-muted-foreground'>
                        Allow new user signups
                      </p>
                    </div>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 border rounded-lg'>
                    <div>
                      <p className='text-sm font-medium'>Email Notifications</p>
                      <p className='text-xs text-muted-foreground'>
                        System email settings
                      </p>
                    </div>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                  <div className='flex justify-between items-center p-3 border rounded-lg'>
                    <div>
                      <p className='text-sm font-medium'>Maintenance Mode</p>
                      <p className='text-xs text-muted-foreground'>
                        Enable system maintenance
                      </p>
                    </div>
                    <span className='text-sm text-muted-foreground'>
                      Coming soon
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Authentication and security configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    Security settings will include password policies, session
                    timeouts, and two-factor authentication configuration.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='permissions' className='space-y-6'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Shield className='h-5 w-5' />
                  Role Management
                </CardTitle>
                <CardDescription>
                  Configure user roles and their permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  <div className='flex items-center justify-between p-3 border rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <Badge variant='destructive'>Admin</Badge>
                      <span className='text-sm'>Full system access</span>
                    </div>
                    <span className='text-xs text-muted-foreground'>
                      Current role
                    </span>
                  </div>
                  <div className='flex items-center justify-between p-3 border rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <Badge variant='default'>Super Admin</Badge>
                      <span className='text-sm'>Ultimate system control</span>
                    </div>
                    <span className='text-xs text-muted-foreground'>
                      Available
                    </span>
                  </div>
                  <div className='flex items-center justify-between p-3 border rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <Badge variant='secondary'>User</Badge>
                      <span className='text-sm'>Standard user access</span>
                    </div>
                    <span className='text-xs text-muted-foreground'>
                      Default
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Permission Matrix</CardTitle>
                <CardDescription>
                  Detailed permission configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    Advanced permission management will allow fine-grained
                    control over user capabilities, including content creation,
                    user management, and system administration.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
