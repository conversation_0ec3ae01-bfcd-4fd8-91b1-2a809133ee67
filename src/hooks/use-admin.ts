'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { authClient } from '@/lib/auth-client'
import { isCurrentUserAdmin, clearSessionCache } from '@/lib/admin-utils'
import { toast } from '@/lib/toast'

export interface AdminUser {
  id: string
  name: string
  email: string
  role?: string
  banned?: boolean
  banReason?: string
  banExpires?: Date
  createdAt: Date
  updatedAt: Date
}

export interface AdminSession {
  id: string
  userId: string
  createdAt: Date
  expiresAt: Date
  ipAddress?: string
  userAgent?: string
  impersonatedBy?: string
}

/**
 * Hook to check if current user is admin using React Query
 */
export function useIsAdmin() {
  const { data: isAdmin = false, isLoading } = useQuery({
    queryKey: ['admin-status'],
    queryFn: async () => {
      const adminStatus = await isCurrentUserAdmin()
      console.log('isAdmin: ', adminStatus)
      return adminStatus
    },
    staleTime: 30000, // Cache for 30 seconds
    retry: false,
  })

  return { isAdmin, isLoading }
}

/**
 * Hook to check if current session is impersonating using React Query
 */
export function useImpersonation() {
  const queryClient = useQueryClient()

  const {
    data: impersonationData = {
      isImpersonating: false,
      impersonatingAdminId: null,
    },
  } = useQuery({
    queryKey: ['impersonation-status'],
    queryFn: async () => {
      try {
        const session = await authClient.getSession()
        const impersonatedBy = (
          session?.data?.session as { impersonatedBy?: string }
        )?.impersonatedBy

        return {
          isImpersonating: !!impersonatedBy,
          impersonatingAdminId: impersonatedBy || null,
        }
      } catch (error) {
        console.error('Error checking impersonation status:', error)
        return {
          isImpersonating: false,
          impersonatingAdminId: null,
        }
      }
    },
    staleTime: 30000, // Cache for 30 seconds
    retry: false,
  })

  const stopImpersonating = useMutation({
    mutationFn: async () => {
      const result = await authClient.admin.stopImpersonating()
      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to stop impersonating')
      }
      return result
    },
    onSuccess: () => {
      toast.impersonationStop()
      clearSessionCache()
      queryClient.invalidateQueries({ queryKey: ['impersonation-status'] })
      queryClient.invalidateQueries({ queryKey: ['admin-status'] })
      // Refresh the page to update the session
      window.location.reload()
    },
    onError: error => {
      toast.adminError('stop impersonating', error)
    },
  })

  return {
    isImpersonating: impersonationData.isImpersonating,
    impersonatingAdminId: impersonationData.impersonatingAdminId,
    stopImpersonating: stopImpersonating.mutate,
    isStoppingImpersonation: stopImpersonating.isPending,
  }
}

/**
 * Hook to list users with admin permissions
 */
export function useAdminUsers(params?: {
  searchValue?: string
  searchField?: 'email' | 'name'
  limit?: number
  offset?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}) {
  return useQuery({
    queryKey: ['admin-users', params],
    queryFn: async () => {
      const result = await authClient.admin.listUsers({
        query: params || {},
      })

      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to fetch users')
      }

      return result.data
    },
    enabled: true, // Will be controlled by the component using this hook
    staleTime: 30000, // Cache for 30 seconds
  })
}

/**
 * Hook to impersonate a user
 */
export function useImpersonateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: string) => {
      const result = await authClient.admin.impersonateUser({
        userId,
      })

      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to impersonate user')
      }

      return result
    },
    onSuccess: () => {
      // We don't have user name here, so we'll use a generic message
      toast.adminSuccess('impersonate user')
      clearSessionCache()
      // Invalidate queries and refresh
      queryClient.invalidateQueries({ queryKey: ['impersonation-status'] })
      queryClient.invalidateQueries({ queryKey: ['admin-status'] })
      // Refresh the page to update the session
      window.location.reload()
    },
    onError: error => {
      toast.adminError('impersonate user', error)
    },
  })
}

/**
 * Hook to ban/unban users
 */
export function useBanUser() {
  const queryClient = useQueryClient()

  const banUser = useMutation({
    mutationFn: async (params: {
      userId: string
      banReason?: string
      banExpiresIn?: number
    }) => {
      const result = await authClient.admin.banUser(params)

      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to ban user')
      }

      return result
    },
    onSuccess: () => {
      toast.adminSuccess('ban user')
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
    },
    onError: error => {
      toast.adminError('ban user', error)
    },
  })

  const unbanUser = useMutation({
    mutationFn: async (userId: string) => {
      const result = await authClient.admin.unbanUser({ userId })

      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to unban user')
      }

      return result
    },
    onSuccess: () => {
      toast.adminSuccess('unban user')
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
    },
    onError: error => {
      toast.adminError('unban user', error)
    },
  })

  return {
    banUser: banUser.mutate,
    unbanUser: unbanUser.mutate,
    isBanning: banUser.isPending,
    isUnbanning: unbanUser.isPending,
  }
}

/**
 * Hook to manage user roles
 */
export function useUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: { userId: string; role: string }) => {
      const result = await authClient.admin.setRole({
        userId: params.userId,
        role: params.role as 'admin' | 'user', // Type assertion for Better Auth
      })

      if (!result.data) {
        throw new Error(result.error?.message || 'Failed to set user role')
      }

      return result
    },
    onSuccess: () => {
      toast.adminSuccess('update user role')
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
    },
    onError: error => {
      toast.adminError('update user role', error)
    },
  })
}
