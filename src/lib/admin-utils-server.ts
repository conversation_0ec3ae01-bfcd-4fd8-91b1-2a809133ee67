import { auth } from '@/lib/auth'
import { headers } from 'next/headers'

/**
 * Server-side function to check if user is admin by user ID
 */
export async function isUserAdminServer(userId: string): Promise<boolean> {
  try {
    // Use the auth context to get user data directly from database
    const ctx = await auth.$context
    const user = await ctx.adapter.findOne({
      model: 'user',
      where: [{ field: 'id', value: userId }],
    })

    if (!user) {
      return false
    }

    const adminRoles = ['admin', 'superadmin']
    const userRole = (user as any).role || 'user'

    return adminRoles.includes(userRole)
  } catch (error) {
    console.error('Error checking server admin status:', error)
    return false
  }
}

/**
 * Server-side function to get admin session
 */
export async function getAdminSessionServer() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    })

    if (!session?.user) {
      throw new Error('Not authenticated')
    }

    const adminRoles = ['admin', 'superadmin']
    const userRole = session.user.role || 'user'
    const isAdmin = adminRoles.includes(userRole)

    if (!isAdmin) {
      throw new Error('Insufficient permissions')
    }

    return session
  } catch (error) {
    console.error('Error getting admin session:', error)
    throw error
  }
}
