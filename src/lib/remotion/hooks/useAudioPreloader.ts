/**
 * useAudioPreloader Hook
 *
 * Preloads audio files from Supabase storage URLs in preview mode to prevent
 * silent gaps at the beginning of audio playback. This is the counterpart to
 * useImagePreloader but specifically for preview mode audio buffering.
 *
 * Uses Remotion's useBufferState().delayPlayback() mechanism to pause the
 * player until audio assets are preloaded and ready for smooth playback.
 */

import { useEffect } from 'react'
import { useBufferState } from 'remotion'
import { preloadAudio } from '@remotion/preload'
import type { Scene } from '../types'

export const useAudioPreloader = (
  scenes: Scene[],
  isRenderingContext: boolean
) => {
  const buffer = useBufferState()

  useEffect(() => {
    // Only run in PREVIEW mode (opposite of image preloader)
    // Audio preloading is not needed during video rendering
    if (isRenderingContext) return

    // Extract unique audio URLs from scenes, excluding base64 and blob URLs
    const audioUrls = Array.from(
      new Set(
        scenes
          .map(scene => {
            // Use same priority as ScenesVideo component
            return scene.voiceover?.audioUrl || scene.voiceSettings?.voiceUrl
          })
          .filter((url): url is string => {
            // Only preload network URLs, skip base64 and blob URLs
            return (
              Boolean(url) &&
              typeof url === 'string' &&
              !url.startsWith('data:') &&
              !url.startsWith('blob:') &&
              url.trim().length > 0
            )
          })
      )
    )

    // If no network audio URLs to preload, return early
    if (audioUrls.length === 0) return

    console.log(`🎵 Preloading ${audioUrls.length} audio files for preview...`)

    // Use Remotion's buffer state to pause playback until audio is ready
    const delayHandle = buffer.delayPlayback()

    // Preload all audio URLs (preloadAudio returns cleanup function, not Promise)
    const cleanupFunctions: (() => void)[] = []

    audioUrls.forEach((url, index) => {
      try {
        console.log(
          `🎵 Preloading audio ${index + 1}/${audioUrls.length}:`,
          url.substring(0, 50) + '...'
        )
        const cleanup = preloadAudio(url)
        cleanupFunctions.push(cleanup)
      } catch (error) {
        console.warn(
          `⚠️ Audio ${index + 1}/${audioUrls.length} failed to preload:`,
          url.substring(0, 50) + '...',
          error
        )
      }
    })

    console.log('✅ All audio preloading initiated')

    // Since preloadAudio doesn't return promises, we use a shorter delay
    // to allow the browser time to start preloading before unblocking
    setTimeout(() => {
      console.log('🎵 Audio preloading delay completed, resuming playback')
      delayHandle.unblock()
    }, 1000) // 1 second delay to allow preloading to start

    // Store cleanup functions for component cleanup
    const allCleanupFunctions = cleanupFunctions

    // Cleanup function to ensure handle is always unblocked and preloads are cleaned up
    return () => {
      try {
        delayHandle.unblock()
      } catch (error) {
        // Handle might already be resolved
        console.debug('Audio preloader cleanup:', error)
      }

      // Clean up all audio preloads
      allCleanupFunctions.forEach((cleanup, index) => {
        try {
          cleanup()
        } catch (error) {
          console.debug(`Audio preload cleanup ${index + 1} failed:`, error)
        }
      })
    }
  }, [scenes, isRenderingContext, buffer])
}
