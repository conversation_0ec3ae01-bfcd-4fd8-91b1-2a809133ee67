'use client'

import { useState, useEffect } from 'react'
import {
  useAdminUsers,
  useImpersonateUser,
  useBanUser,
  useUserRole,
} from '@/hooks/use-admin'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  MoreHorizontal,
  <PERSON>r<PERSON>heck,
  UserX,
  Shield,
  Search,
  Loader2,
  Crown,
} from 'lucide-react'
import { formatUserRole } from '@/lib/admin-utils'
import { format } from 'date-fns'
import { Skeleton } from '@/components/ui/skeleton'

interface BanDialogState {
  isOpen: boolean
  userId: string
  userName: string
}

interface RoleDialogState {
  isOpen: boolean
  userId: string
  userName: string
  currentRole: string
}

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [searchField, setSearchField] = useState<'email' | 'name'>('email')
  const [banDialog, setBanDialog] = useState<BanDialogState>({
    isOpen: false,
    userId: '',
    userName: '',
  })
  const [roleDialog, setRoleDialog] = useState<RoleDialogState>({
    isOpen: false,
    userId: '',
    userName: '',
    currentRole: '',
  })
  const [banReason, setBanReason] = useState('')
  const [banDuration, setBanDuration] = useState<string>('')
  const [newRole, setNewRole] = useState('')

  // Debounce search term with 600ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 600)

    return () => clearTimeout(timer)
  }, [searchTerm])

  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useAdminUsers({
    searchValue: debouncedSearchTerm,
    searchField,
    limit: 50,
  })

  const impersonateUser = useImpersonateUser()
  const { banUser, unbanUser, isBanning, isUnbanning } = useBanUser()
  const setUserRole = useUserRole()

  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleImpersonate = (userId: string) => {
    impersonateUser.mutate(userId)
  }

  const handleBanUser = () => {
    const banExpiresIn = banDuration
      ? parseInt(banDuration) * 24 * 60 * 60
      : undefined

    banUser({
      userId: banDialog.userId,
      banReason: banReason || undefined,
      banExpiresIn,
    })

    // Close modal and reset state
    closeBanDialog()
  }

  const handleUnbanUser = (userId: string) => {
    unbanUser(userId)
  }

  const handleSetRole = () => {
    setUserRole.mutate({
      userId: roleDialog.userId,
      role: newRole,
    })

    // Close modal and reset state
    closeRoleDialog()
  }

  // Helper functions to properly close modals and reset state
  const closeBanDialog = () => {
    setBanDialog({ isOpen: false, userId: '', userName: '' })
    setBanReason('')
    setBanDuration('')
  }

  const closeRoleDialog = () => {
    setRoleDialog({ isOpen: false, userId: '', userName: '', currentRole: '' })
    setNewRole('')
  }

  const getBadgeVariant = (role?: string) => {
    if (!role) return 'secondary'
    if (role.includes('admin')) return 'destructive'
    if (role.includes('superadmin')) return 'default'
    return 'secondary'
  }

  // Skeleton loader for table rows
  const SkeletonRow = () => (
    <TableRow>
      <TableCell>
        <div>
          <Skeleton className='h-4 w-32 mb-1' />
          <Skeleton className='h-3 w-48' />
        </div>
      </TableCell>
      <TableCell>
        <Skeleton className='h-5 w-16' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-5 w-16' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-8 w-8 rounded-full' />
      </TableCell>
    </TableRow>
  )

  // Show loading indicator in search input
  const isSearching = searchTerm !== debouncedSearchTerm

  if (error) {
    return (
      <div className='p-8 text-center'>
        <p className='text-red-600'>Error loading users: {error.message}</p>
        <Button onClick={() => refetch()} className='mt-4'>
          Retry
        </Button>
      </div>
    )
  }

  const users = usersData?.users || []

  return (
    <div className='space-y-6'>
      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
          {isSearching && (
            <Loader2 className='absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground' />
          )}
          <Input
            placeholder='Search users...'
            value={searchTerm}
            onChange={e => handleSearch(e.target.value)}
            className={`pl-10 ${isSearching ? 'pr-10' : ''}`}
          />
        </div>
        <Select
          value={searchField}
          onValueChange={(value: 'email' | 'name') => setSearchField(value)}
        >
          <SelectTrigger className='w-32'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='email'>Email</SelectItem>
            <SelectItem value='name'>Name</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Users Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className='w-[70px]'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? // Show skeleton rows while loading
                Array.from({ length: 5 }).map((_, index) => (
                  <SkeletonRow key={index} />
                ))
              : users.map(user => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className='font-medium'>{user.name}</div>
                        <div className='text-sm text-muted-foreground'>
                          {user.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getBadgeVariant(user.role)}>
                        {formatUserRole(user.role)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {user.banned ? (
                        <Badge variant='destructive'>Banned</Badge>
                      ) : (
                        <Badge variant='outline'>Active</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleImpersonate(user.id)}
                            disabled={impersonateUser.isPending}
                          >
                            <Crown className='mr-2 h-4 w-4' />
                            Impersonate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={e => {
                              
                              setRoleDialog({
                                isOpen: true,
                                userId: user.id,
                                userName: user.name,
                                currentRole: user.role || 'user',
                              })
                            }}
                          >
                            <Shield className='mr-2 h-4 w-4' />
                            Change Role
                          </DropdownMenuItem>
                          {user.banned ? (
                            <DropdownMenuItem
                              onClick={() => handleUnbanUser(user.id)}
                              disabled={isUnbanning}
                            >
                              <UserCheck className='mr-2 h-4 w-4' />
                              Unban User
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={e => {
                                e.preventDefault()
                                e.stopPropagation()
                                setBanDialog({
                                  isOpen: true,
                                  userId: user.id,
                                  userName: user.name,
                                })
                              }}
                              disabled={isBanning}
                            >
                              <UserX className='mr-2 h-4 w-4' />
                              Ban User
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
          </TableBody>
        </Table>
      </div>

      {/* Ban User Dialog */}
      <Dialog
        open={banDialog.isOpen}
        onOpenChange={open => {
          if (!open) {
            closeBanDialog()
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ban User</DialogTitle>
            <DialogDescription>
              Are you sure you want to ban {banDialog.userName}? This will
              prevent them from accessing the application.
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4'>
            <div>
              <Label htmlFor='ban-reason'>Reason (optional)</Label>
              <Textarea
                id='ban-reason'
                placeholder='Enter reason for ban...'
                value={banReason}
                onChange={e => setBanReason(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor='ban-duration'>Duration (days, optional)</Label>
              <Input
                id='ban-duration'
                type='number'
                placeholder='Leave empty for permanent ban'
                value={banDuration}
                onChange={e => setBanDuration(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={closeBanDialog}>
              Cancel
            </Button>
            <Button
              variant='destructive'
              onClick={handleBanUser}
              disabled={isBanning}
            >
              {isBanning ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : null}
              Ban User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Role Dialog */}
      <Dialog
        open={roleDialog.isOpen}
        onOpenChange={open => {
          if (!open) {
            closeRoleDialog()
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Change the role for {roleDialog.userName}. Current role:{' '}
              {formatUserRole(roleDialog.currentRole)}
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4'>
            <div>
              <Label htmlFor='new-role'>New Role</Label>
              <Select value={newRole} onValueChange={setNewRole}>
                <SelectTrigger>
                  <SelectValue placeholder='Select a role' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='user'>User</SelectItem>
                  <SelectItem value='admin'>Admin</SelectItem>
                  <SelectItem value='superadmin'>Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={closeRoleDialog}>
              Cancel
            </Button>
            <Button
              onClick={handleSetRole}
              disabled={setUserRole.isPending || !newRole}
            >
              {setUserRole.isPending ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : null}
              Update Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
